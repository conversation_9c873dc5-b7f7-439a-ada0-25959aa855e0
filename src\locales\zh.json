{"Metadata": {"title": "MidJourney 提示词生成器", "description": "提出您的想法，让 AI 发挥想象力，每个人都能成为提示词大师。"}, "Prompt": {"name": "提示词", "title": "生成令人惊叹的图片提示词", "description": "提出您的想法，让 AI 发挥想象力，每个人都能成为提示词大师。", "openSource": "开源代码", "allFree": "完全免费", "noLogin": "无需登录", "unlimitedGenerations": "无限生成", "tabs": {"generator": "生成器", "history": "历史"}, "generator": {"label": "您的创意", "presetParameters": "预设参数", "imageToPrompt": "图片转提示词", "copyPrompt": "复制提示词", "copyOptimizedPrompt": "复制优化后提示词", "clear": "清空", "imagine": "想象", "aspectRatios": "宽高比", "style": "风格", "default": "默认", "moviesAndDisplays": "电影和显示器", "socialPlatforms": "社交平台", "fullScreenMobilePhone": "全面屏手机", "print": "打印", "instagram": "Instagram", "earlyVideosAndPhotos": "早期影像、照片", "standardSLRCamera": "标准单反相机", "panoramicPhotography": "全景摄影", "portraitPhotography": "人像拍摄", "noStyle": "无风格", "photography": "摄影", "film": "电影", "animeStyle": "动漫", "comics": "漫画", "studioGhibli": "吉卜力", "cyberpunk": "赛博朋克", "pixelArt": "像素艺术", "minimalistDesign": "极简设计", "surrealism": "超现实", "realism": "写实", "washpainting": "水墨画", "chineseTraditionalPainting": "国风绘画", "sketch": "素描", "watercolorPainting": "水彩画", "oilPainting": "油画", "illustration": "插画", "manuscript": "手稿", "ukiyoe": "浮世绘", "claymation": "黏土动画", "kirigami": "剪纸", "lowPoly": "低多边形", "holographic": "全息色彩", "3DModel": "3D 模型"}, "history": {"title": "今日历史"}, "faq": {"title": "常见问题", "theFirstQuestion": "这个提示词生成器有什么作用？", "theFirstAnswer": "它能根据您的简单描述，在 AI 的辅助下生成大师级的 MidJourney 提示词。这些提示词可以生成细节丰富，视觉风格多样的图片。", "theSecondQuestion": "这个提示词生成器的工作原理是什么？", "theSecondAnswer": "AI 根据您的想法进行创意发挥，结合预设参数，生成完美的 MidJourney 提示词。", "theThirdQuestion": "生成的提示词可以在其他图片生成模型中使用吗？", "theThirdAnswer": "图片提示词本质上是对图片内容的描述。这类描述具有一定的通用性，您可以将生成的提示词用于其他的图片生成模型。", "theFourthQuestion": "提示生成器可以免费使用吗？", "theFourthAnswer": "是的，您可以在不登录的情况下<strong>完全免费</strong>使用。没有隐藏费用，无需信用卡，也没有使用限制。", "theFifthQuestion": "这个提示词生成器是由哪个 AI 服务提供？", "theFifthAnswer": "目前，这个提示词生成器是由 <link>Pollinations AI</link> 提供技术支持。它提供了多种免费的 AI 模型，本项目通过这些免费模型来生成提示词。您可以在文本输入框的右上角选择您偏好的 AI 模型。"}, "disclaimer": "提示词由 AI 生成，可能不准确或内容不当。", "loadMore": "加载更多历史", "noHistory": "没有历史"}, "Component": {"HistoryItem": {"copy": "复制", "copied": "已复制", "delete": "删除"}}, "Error": {"requestFailed": "请求失败", "promptGenerationError": "提示词生成失败", "serverError": "服务器错误"}}