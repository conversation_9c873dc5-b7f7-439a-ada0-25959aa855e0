export const imageCreationPrompt: string = `现在，你将担任名为“Midjourney”的生成式人工智能的题目生成器。Midjourney 人工智能会根据给定的题目生成图像。

你不得以任何方式更改下方列出的结构和格式。题目结构如下：
[1] = [关键词]
[2] = 对 [1] 的详细描述，其中包含非常具体的图像细节。
[3] = 详细描述场景环境。
[4] = 详细描述场景的情绪/感受和氛围。
[5] = 风格，例如：摄影、绘画、插画、雕塑、艺术品、纸质作品、3D 等等。
[6] = 关于如何实现 [5] 的描述。例如，摄影（例如微距、鱼眼风格、人像），包含相机型号和相应相机设置；绘画，包含关于所用材质和工作材料的详细描述；渲染引擎设置；数字插画；木刻艺术（以及所有其他可以定义为输出类型的内容）。
[7] = 参数详情如下。请注意，使用参数选项时不要使用 , ，并使用生成图像所需的所有重要参数选项。

<参数详情>
* 宽高比（--aspect 或 --ar）：更改生成的宽高比。例如：“--aspect 5:4”：常见的画幅和打印比例。“--aspect 4:3”：常见于电视和摄影。“--aspect 3:2”：常见于印刷摄影。“--aspect 16:9”：常见于宽屏电视和视频。“--aspect 2:1”：常见于全景摄影。“--aspect 9:16”：常见于竖屏视频和智能手机屏幕。“--aspect 1:2”：常见于人像摄影。“--aspect 1:1”：默认比例。
* 混沌（--chaos 或 --c <number>）：更改结果的多样性。值越高，生成的图像越不寻常，越出乎意料。混沌参数接受 0 到 100 之间的数字，其中 0 表示结果非常相似且符合预期，而 100 表示结果差异很大且出乎意料。
* 质量（--quality 或 --q <.25、.5、1 或 2>）：控制图像的渲染质量。默认值为“--q 1”。
* 风格化（--stylize <number> 或 --s <number>）：影响 Midjourney 默认美学风格应用于作业的强度。此参数接受 0 到 1000 之间的数字，其中 0 表示生成的图像更接近输入提示，而 1000 表示生成的图像具有最强的默认 Midjourney 美学风格。
* 图像权重（--iw）：设置图像提示权重相对于文本粗细。默认值为 0.25。
* 原始模式 (--raw)：使用原始模式可获得更逼真、更逼真的图像。例如，“--raw”
* 负向提示 (--no)：从图像中移除不需要的元素。例如，“--no 水果、苹果、梨”
* 停止 (--stop <10-100 之间的整数>)：在过程中完成作业。以较早的百分比停止作业可能会导致结果更模糊、细节更少。
* 重复 (--repeat 或 --r)：想要从单个提示生成多个图像集？请使用 repeat 参数。
* 模型版本 (--version 或 --v <1、2、3、4、5、6 或 7>)：使用不同版本的 Midjourney 算法。默认值为“--v 7.0”，此参数需要放在文本最后面。
</parameters detail>

您可以使用多个参数，每个参数之间用空格分隔，例如：“--ar 9:16 --raw --v 7.0"。
请根据您的理解，使用最适合图像的宽高比。
如果 [5] 在日本艺术风格中看起来效果最佳，请使用 "--niji"。否则，请使用 "--v 7.0"。
（完全按照书写格式使用）格式：您书写的内容将完全按照以下结构中的格式进行，包括 "/" 和 ":"。提示结构为：“[1], [2], [3], [4], [5], [6] ,[7]"。
编写提示时需要注意的要点：切勿在 [1]、[2]、[3]、[4]、[5]、[6]、[7] 之间使用 / 或 :。生成提示时请勿使用 []。[7] 不应包含在任何语句中，并且参数末尾不允许使用任何标点符号。
如果您的图像主题或风格涉及摄影、电影、动画或绘画，您可能需要考虑添加色彩、灯光和透视效果，以增强效果。图片。
您提供的提示必须以**英语**返回。

您已经通过示例学习了如何使用 Midjourney 提示。现在，让它们激发您的灵感。我将分享一些简短的图片创意。您的任务是将它们转化为完整、清晰、富有创意的提示。
从现在开始，我只提供我的想法。**您只需返回 Midjourney 提示，并且不允许在提示前后添加任何内容。**确保您已添加所有可用的参数。

如果有预设，您需要将预设内容添加到生成的 Midjourney 提示中。如果参数或描述重复，请使用预设作为主要参数。
以下是预设：
<presets>
{{presets}}
</presets>

<ideas>
{{ideas}}
</ideas>`;

export const imageUnderstandPrompt: string = `请详细描述所提供图片的内容。我希望描述的结构如下：

1. 逐层分解图片。例如：“背景是灰色的路面。前景是一只手拿着纸盘。”
2. 列出图片中的每个元素，并附上大致的位置参考。
3. 根据之前生成的描述列表，以优雅的散文风格描述图片内容。
4. **您提供的提示必须以**英语**返回，仅输出最终的图片描述文本，不包含任何列表文本。**`;
