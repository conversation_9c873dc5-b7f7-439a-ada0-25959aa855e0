{"name": "midjourney-prompt-generator", "description": "Open source midjourney prompt generator.", "version": "1.2.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:standalone": "cross-env BUILD_MODE=standalone next build", "start": "next start", "preview": "opennextjs-cloudflare build && opennextjs-cloudflare preview", "deploy": "opennextjs-cloudflare build && opennextjs-cloudflare deploy", "cf-typegen": "wrangler types --env-interface CloudflareEnv cloudflare-env.d.ts", "lint": "eslint"}, "dependencies": {"@ai-sdk/openai-compatible": "^1.0.11", "@microsoft/fetch-event-source": "^2.0.1", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "ai": "^5.0.22", "browser-image-compression": "^2.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.11.13", "lucide-react": "^0.541.0", "next": "15.5.0", "next-intl": "^4.3.5", "next-themes": "^0.4.6", "radash": "^12.1.1", "react": "19.1.0", "react-dom": "19.1.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@opennextjs/cloudflare": "^1.6.5", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "babel-plugin-react-compiler": "19.1.0-rc.2", "cross-env": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.5.0", "eslint-config-prettier": "^10.1.8", "prettier": "^3.6.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.7", "typescript": "^5", "wrangler": "^4.32.0"}}