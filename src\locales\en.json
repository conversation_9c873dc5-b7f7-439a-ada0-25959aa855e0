{"Metadata": {"title": "MidJourney Prompt Generator", "description": "Give your ideas and let AI use its imagination. Everyone can be a master of prompt."}, "Prompt": {"name": "Prompt", "title": "Create Amazing Image Prompt", "description": "Give your ideas and let AI use its imagination. Everyone can be a master of prompt.", "openSource": "Open Source", "allFree": "100% Free", "noLogin": "No Login", "unlimitedGenerations": "Unlimited Generations", "tabs": {"generator": "Generator", "history": "History"}, "generator": {"label": "Your ideas", "presetParameters": "Preset Parameters", "imageToPrompt": "Image to prompt", "copyPrompt": "Copy prompt", "copyOptimizedPrompt": "Copy optimized prompt", "clear": "Clear", "imagine": "Imagine", "aspectRatios": "Aspect Ratios", "style": "Style", "default": "<PERSON><PERSON><PERSON>", "moviesAndDisplays": "Movies and Displays", "socialPlatforms": "Social Platforms", "fullScreenMobilePhone": "Full screen mobile phone", "print": "Print", "instagram": "Instagram", "earlyVideosAndPhotos": "Early videos and photos", "standardSLRCamera": "Standard SLR camera", "panoramicPhotography": "Panoramic photography", "portraitPhotography": "Portrait photography", "noStyle": "No style", "photography": "Photography", "film": "Film", "animeStyle": "Anime", "comics": "Comics", "studioGhibli": "Studio Ghibli", "cyberpunk": "Cyberpunk", "pixelArt": "Pixel art", "minimalistDesign": "Minimalist", "surrealism": "Surrealism", "realism": "Realism", "washpainting": "Washpainting", "chineseTraditionalPainting": "Chinese traditional painting", "sketch": "Sketch", "watercolorPainting": "Watercolor painting", "oilPainting": "Oil painting", "illustration": "Illustration", "manuscript": "Manuscript", "ukiyoe": "Ukiyoe", "claymation": "Claymation", "kirigami": "<PERSON><PERSON><PERSON>", "lowPoly": "Low-poly", "holographic": "Holographic", "3DModel": "3D model"}, "history": {"title": "Today's History"}, "faq": {"title": "Frequently Asked Questions", "theFirstQuestion": "What does this prompt generator do?", "theFirstAnswer": "It can generate master-level MidJourney prompt with AI assistance based on the user's simple description. Such prompts can make the pictures you generate have rich picture details and diverse visual styles.", "theSecondQuestion": "How does this prompt generator work?", "theSecondAnswer": "The AI takes your ideas and combines them with preset parameters to generate the perfect MidJourney prompts.", "theThirdQuestion": "Can the generated prompts be used in other image generation models?", "theThirdAnswer": "Image prompts are essentially descriptions of the image content. Such descriptions are generally universal, and you can use the generated prompts for other image generation models.", "theFourthQuestion": "Is the prompt generator free to use?", "theFourthAnswer": "Yes, you can use it for <strong>free</strong> without login. There are no hidden fees, no credit card required, and no usage limits.", "theFifthQuestion": "Which AI provider powers this prompt generator?", "theFifthAnswer": "Currently, the prompt generator is powered by <link>Pollinations AI</link>. It provides a variety of free AI models, and this project generates prompts based on these free AI models. You can select your favorite AI model in the upper right corner of the textarea."}, "disclaimer": "The prompts are generated by AI and may be inaccurate or inappropriate.", "loadMore": "Load More History", "noHistory": "No history"}, "Component": {"HistoryItem": {"copy": "Copy", "copied": "<PERSON>pied", "delete": "Delete"}}, "Error": {"requestFailed": "Request failed", "promptGenerationError": "Prompt generation error", "serverError": "Server Error"}}